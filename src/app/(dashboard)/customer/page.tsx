'use client'

import { useAuth } from '@/hooks/useAuth'
import { useOrders } from '@/hooks/useOrders'
import { useState, useEffect, useMemo } from 'react'
import { MapPin, Clock, Car, Plus, Navigation, Phone } from 'lucide-react'
import { OrderDocument, OrderStatus } from '@/types/database'
import { CreateOrderData } from '@/services/OrderService'
import { RideTrackingMap, useLocationTracking } from '@/components/maps/RideTrackingMap'
import { LocationSearch } from '@/components/maps/LocationSearch'
import toast from 'react-hot-toast'

export default function CustomerDashboard() {
  const { userProfile } = useAuth()
  const { orders, currentOrder, createOrder, subscribeToUserOrders, subscribeToOrder } = useOrders()
  const [showBookingForm, setShowBookingForm] = useState(false)
  const [activeOrder, setActiveOrder] = useState<OrderDocument | null>(null)
  const { driverLocation, isTracking } = useLocationTracking(activeOrder?.id)

  // Memoize location objects to prevent map re-renders
  const pickupLocation = useMemo(() => {
    if (!activeOrder) return null
    return {
      lat: activeOrder.locations.pickup.coordinates.latitude,
      lng: activeOrder.locations.pickup.coordinates.longitude
    }
  }, [activeOrder?.locations.pickup.coordinates.latitude, activeOrder?.locations.pickup.coordinates.longitude])

  const destinationLocation = useMemo(() => {
    if (!activeOrder) return null
    return {
      lat: activeOrder.locations.destination.coordinates.latitude,
      lng: activeOrder.locations.destination.coordinates.longitude
    }
  }, [activeOrder?.locations.destination.coordinates.latitude, activeOrder?.locations.destination.coordinates.longitude])

  // Subscribe to user orders when component mounts
  useEffect(() => {
    if (userProfile?.id) {
      subscribeToUserOrders(userProfile.id, 'customer')
    }
  }, [userProfile?.id]) // Remove subscribeToUserOrders from dependencies

  // Determine active order from orders list
  useEffect(() => {
    const active = orders.find(order =>
      ['pending', 'searching', 'assigned', 'driver_arriving', 'driver_arrived', 'picked_up', 'in_progress'].includes(order.status.current)
    )
    setActiveOrder(active || null)
  }, [orders])

  // Subscribe when active order ID changes
  useEffect(() => {
    if (activeOrder?.id) {
      subscribeToOrder(activeOrder.id)
    }
  }, [activeOrder?.id])

  const handleBookRide = async (orderData: CreateOrderData) => {
    const orderId = await createOrder(orderData)
    if (orderId) {
      setShowBookingForm(false)
      toast.success('Ride booked successfully! Looking for nearby drivers...')
    }
  }

  const getStatusColor = (status: OrderStatus) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800'
      case 'searching': return 'bg-blue-100 text-blue-800'
      case 'assigned': return 'bg-green-100 text-green-800'
      case 'driver_arriving': return 'bg-orange-100 text-orange-800'
      case 'driver_arrived': return 'bg-purple-100 text-purple-800'
      case 'picked_up': return 'bg-indigo-100 text-indigo-800'
      case 'in_progress': return 'bg-cyan-100 text-cyan-800'
      case 'completed': return 'bg-green-100 text-green-800'
      case 'cancelled': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusText = (status: OrderStatus) => {
    switch (status) {
      case 'pending': return 'Order Placed'
      case 'searching': return 'Finding Driver'
      case 'assigned': return 'Driver Assigned'
      case 'driver_arriving': return 'Driver Coming'
      case 'driver_arrived': return 'Driver Arrived'
      case 'picked_up': return 'Picked Up'
      case 'in_progress': return 'On the Way'
      case 'completed': return 'Completed'
      case 'cancelled': return 'Cancelled'
      default: return status
    }
  }

  if (!userProfile) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-yellow-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            Welcome, {userProfile.profile.firstName}!
          </h1>
          <p className="text-gray-600 mt-2">Book your ride or track your current trip</p>
        </div>
        {!activeOrder && (
          <button
            onClick={() => setShowBookingForm(true)}
            className="bg-gradient-to-r from-yellow-500 to-yellow-600 text-black px-6 py-3 rounded-xl font-semibold hover:from-yellow-600 hover:to-yellow-700 transition-all duration-300 transform hover:scale-105 shadow-lg"
          >
            <Plus className="w-5 h-5 inline mr-2" />
            Book a Ride
          </button>
        )}
      </div>

      {/* Active Order Card */}
      {activeOrder && (
        <div className="glass-effect rounded-2xl p-6 border border-white/20">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-semibold text-gray-900">Current Ride</h2>
            <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(activeOrder.status.current)}`}>
              {getStatusText(activeOrder.status.current)}
            </span>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Trip Details */}
            <div className="space-y-4">
              <div className="flex items-start space-x-3">
                <div className="w-3 h-3 rounded-full bg-green-500 mt-2"></div>
                <div>
                  <p className="text-sm text-gray-500">Pickup</p>
                  <p className="font-medium text-gray-900">{activeOrder.locations.pickup.address}</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <div className="w-3 h-3 rounded-full bg-red-500 mt-2"></div>
                <div>
                  <p className="text-sm text-gray-500">Destination</p>
                  <p className="font-medium text-gray-900">{activeOrder.locations.destination.address}</p>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <Clock className="w-4 h-4 text-gray-400" />
                <div>
                  <p className="text-sm text-gray-500">Estimated Fare</p>
                  <p className="font-medium text-gray-900">JD {activeOrder.pricing.total.toFixed(2)}</p>
                </div>
              </div>
            </div>

            {/* Driver Details */}
            {activeOrder.driver && (
              <div className="space-y-4">
                <div className="flex items-center space-x-3">
                  <div className="w-12 h-12 rounded-full bg-yellow-100 flex items-center justify-center">
                    {activeOrder.driver.avatar ? (
                      <img 
                        src={activeOrder.driver.avatar} 
                        alt={activeOrder.driver.name}
                        className="w-12 h-12 rounded-full object-cover"
                      />
                    ) : (
                      <span className="text-lg font-semibold text-yellow-800">
                        {activeOrder.driver.name.split(' ').map(n => n[0]).join('')}
                      </span>
                    )}
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">{activeOrder.driver.name}</p>
                    <p className="text-sm text-gray-500">Your Driver</p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-3">
                  <Car className="w-4 h-4 text-gray-400" />
                  <div>
                    <p className="text-sm text-gray-500">Vehicle</p>
                    <p className="font-medium text-gray-900">
                      {activeOrder.driver.vehicle.make} {activeOrder.driver.vehicle.model}
                    </p>
                    <p className="text-sm text-gray-500">{activeOrder.driver.vehicle.plateNumber}</p>
                  </div>
                </div>

                <div className="flex space-x-2">
                  <button className="flex-1 bg-green-500 text-white px-4 py-2 rounded-lg hover:bg-green-600 transition-colors flex items-center justify-center">
                    <Phone className="w-4 h-4 mr-2" />
                    Call Driver
                  </button>
                  <button className="flex-1 bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors flex items-center justify-center">
                    <Navigation className="w-4 h-4 mr-2" />
                    Track
                  </button>
                </div>
              </div>
            )}
          </div>

          {/* Live Tracking Map */}
          <div className="mt-6">
            {pickupLocation && destinationLocation && (
              <RideTrackingMap
                pickup={pickupLocation}
                destination={destinationLocation}
                driverLocation={driverLocation || undefined}
                showRoute={true}
                className="h-64"
              />
            )}
          </div>
        </div>
      )}

      {/* Recent Rides */}
      <div className="glass-effect rounded-2xl p-6 border border-white/20">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Recent Rides</h2>
        
        {orders.length === 0 ? (
          <div className="text-center py-8">
            <Car className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500">No rides yet</p>
            <p className="text-sm text-gray-400">Book your first ride to get started!</p>
          </div>
        ) : (
          <div className="space-y-4">
            {orders.slice(0, 5).map((order) => (
              <div key={order.id} className="flex items-center justify-between p-4 bg-white/50 rounded-xl">
                <div className="flex items-center space-x-4">
                  <div className="flex flex-col items-center">
                    <div className="w-2 h-2 rounded-full bg-green-500"></div>
                    <div className="w-px h-6 bg-gray-300"></div>
                    <div className="w-2 h-2 rounded-full bg-red-500"></div>
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">
                      {order.locations.pickup.address} → {order.locations.destination.address}
                    </p>
                    <p className="text-sm text-gray-500">
                      {order.metadata?.createdAt?.toDate().toLocaleDateString()}
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="font-medium text-gray-900">JD {order.pricing.total.toFixed(2)}</p>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(order.status.current)}`}>
                    {getStatusText(order.status.current)}
                  </span>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Booking Form Modal */}
      {showBookingForm && (
        <BookingModal
          onClose={() => setShowBookingForm(false)}
          onBook={handleBookRide}
          customerId={userProfile.id}
        />
      )}
    </div>
  )
}

// Simple booking modal component
function BookingModal({
  onClose,
  onBook,
  customerId
}: {
  onClose: () => void
  onBook: (data: CreateOrderData) => void
  customerId: string
}) {
  const [pickupLocation, setPickupLocation] = useState<{address: string, coordinates: {lat: number, lng: number}} | null>(null)
  const [destinationLocation, setDestinationLocation] = useState<{address: string, coordinates: {lat: number, lng: number}} | null>(null)
  const [serviceType, setServiceType] = useState<'economy' | 'standard' | 'premium'>('standard')

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()

    if (!pickupLocation || !destinationLocation) {
      toast.error('Please select both pickup and destination locations')
      return
    }

    const orderData: CreateOrderData = {
      customerId,
      pickup: {
        address: pickupLocation.address,
        coordinates: { lat: pickupLocation.coordinates.lat, lng: pickupLocation.coordinates.lng }
      },
      destination: {
        address: destinationLocation.address,
        coordinates: { lat: destinationLocation.coordinates.lat, lng: destinationLocation.coordinates.lng }
      },
      serviceType,
      paymentMethod: 'cash'
    }

    onBook(orderData)
  }

  return (
    <div className="fixed inset-0 backdrop-blur-md flex items-center justify-center z-50">
      <div className="bg-white/90 backdrop-blur-sm rounded-2xl p-6 w-full max-w-md mx-4 shadow-2xl border border-white/20">
        <h3 className="text-xl font-semibold mb-4">Book a Ride</h3>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Pickup Location</label>
            <LocationSearch
              placeholder="Enter pickup address"
              value={pickupLocation?.address || ''}
              onChange={(location) => setPickupLocation(location)}
              className="w-full"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Destination</label>
            <LocationSearch
              placeholder="Enter destination address"
              value={destinationLocation?.address || ''}
              onChange={(location) => setDestinationLocation(location)}
              className="w-full"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Service Type</label>
            <select
              value={serviceType}
              onChange={(e) => setServiceType(e.target.value as any)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-transparent"
            >
              <option value="economy">Economy (JD 1.5 base)</option>
              <option value="standard">Standard (JD 2.0 base)</option>
              <option value="premium">Premium (JD 3.0 base)</option>
            </select>
          </div>
          
          <div className="flex space-x-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="flex-1 px-4 py-2 bg-yellow-500 text-black rounded-lg hover:bg-yellow-600 font-medium"
            >
              Book Ride
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}
